import { useEffect, useRef } from 'react';
import { TradeStatus } from '../types/trading';
import { useTradingStore } from '../store/tradingStore';
import { getMockDataService } from '../services/mockDataService';
import toast from 'react-hot-toast';

export const useDemo = () => {
  const {
    setConnectionStatus,
    updateMarketData,
    updateChartData,
    setAccount,
    addAISignal,
    addTrade,
    updateTrade,
    currentMarket,
  } = useTradingStore();

  const mockService = useRef(getMockDataService());
  const tickInterval = useRef<NodeJS.Timeout | null>(null);
  const signalInterval = useRef<NodeJS.Timeout | null>(null);
  const tradeInterval = useRef<NodeJS.Timeout | null>(null);
  const resolveInterval = useRef<NodeJS.Timeout | null>(null);
  const isDemoActive = useRef<boolean>(false);

  // Function to start the demo mode
  const startDemoMode = () => {
    if (isDemoActive.current) return; // Already running

    isDemoActive.current = true;
    
    // Set connection status to connected
    setConnectionStatus(true);
    
    // Set mock account
    const account = mockService.current.generateMockAccount();
    setAccount(account);
    
    // Load initial chart data
    const chartData = mockService.current.getChartData();
    updateChartData(currentMarket, chartData);
    
    // Set initial market data
    const marketData = mockService.current.getCurrentMarketData();
    if (marketData) {
      updateMarketData(currentMarket, marketData);
    }
    
    // Generate some initial AI signals
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        const signal = mockService.current.generateAISignal();
        addAISignal(signal);
      }, i * 2000);
    }
    
    // Generate some initial trades
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        const trade = mockService.current.generateMockTrade();
        addTrade(trade);
      }, i * 1000);
    }
    
    // Start price tick simulation
    tickInterval.current = setInterval(() => {
      mockService.current.generateNewTick();
      
      const chartData = mockService.current.getChartData();
      updateChartData(currentMarket, chartData);
      
      const marketData = mockService.current.getCurrentMarketData();
      if (marketData) {
        updateMarketData(currentMarket, marketData);
      }
    }, 3000); // Update every 3 seconds

    // Start AI signal generation
    signalInterval.current = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance every interval
        const signal = mockService.current.generateAISignal();
        addAISignal(signal);
        
        if (signal.confidence > 0.8) {
          toast.success(`Strong AI signal: ${signal.direction.toUpperCase()}`, {
            duration: 4000,
          });
        }
      }
    }, 15000); // Check every 15 seconds

    // Start trade generation (simulate other traders)
    tradeInterval.current = setInterval(() => {
      if (Math.random() > 0.8) { // 20% chance every interval
        const trade = mockService.current.generateMockTrade();
        addTrade(trade);
      }
    }, 20000); // Check every 20 seconds

    // Resolver: periodically update active trades to won/lost when duration elapsed
    resolveInterval.current = setInterval(() => {
      const { activeTrades } = useTradingStore.getState();
      activeTrades.forEach(trade => {
        const elapsed = Date.now() - trade.timestamp;
        const targetMs = trade.duration * 1000; // treat duration as seconds in demo
        if (elapsed >= targetMs) {
          const outcomeWon = Math.random() > 0.5;
          const exitPrice = trade.entryPrice + (Math.random() - 0.5) * 5;
          const profit = outcomeWon ? trade.stake * (0.8 + Math.random() * 0.4) : -trade.stake;
          updateTrade(trade.id, {
            status: outcomeWon ? TradeStatus.Won : TradeStatus.Lost,
            exitPrice,
            profit,
            payout: outcomeWon ? profit : undefined,
          });
        }
      });
    }, 3000); // check every 3s
    
    toast.success('Demo mode activated - Using simulated data');
  };
  
  // Function to stop the demo mode
  const stopDemoMode = () => {
    isDemoActive.current = false;
    
    // Clear all intervals
    if (tickInterval.current) {
      clearInterval(tickInterval.current);
      tickInterval.current = null;
    }
    if (signalInterval.current) {
      clearInterval(signalInterval.current);
      signalInterval.current = null;
    }
    if (tradeInterval.current) {
      clearInterval(tradeInterval.current);
      tradeInterval.current = null;
    }
    if (resolveInterval.current) {
      clearInterval(resolveInterval.current);
      resolveInterval.current = null;
    }
  };
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopDemoMode();
    };
  }, []);

  return {
    isDemo: isDemoActive.current,
    activateDemo: startDemoMode,
    deactivateDemo: stopDemoMode
  };
};
