import React, { useState } from 'react';
import {
  Brain,
  TrendingUp,
  TrendingDown,
  Clock,
  Target,
  Activity,
  DollarSign,
  BarChart3,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
} from 'lucide-react';
import { useTradingStore } from '../store/tradingStore';
import { useTrading } from '../hooks/useTrading';
import { AISignal, Trade } from '../types/trading';
import ContractDetailsDialog from './ContractDetailsDialog';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const [selectedTrade, setSelectedTrade] = useState<Trade | null>(null);

  const [activeTab, setActiveTab] = useState<'signals' | 'trades' | 'stats'>('signals');
  const { getLatestSignals, getTradeHistory, getTradingStats } = useTrading();

  const signals = getLatestSignals(10);
  const trades = getTradeHistory(20);
  const stats = getTradingStats();

  const tabs = [
    { id: 'signals', label: 'AI Signals', icon: Brain },
    { id: 'trades', label: 'Trades', icon: Activity },
    { id: 'stats', label: 'Stats', icon: BarChart3 },
  ];

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getSignalIcon = (direction: string) => {
    switch (direction) {
      case 'up':
      case 'rise':
        return <TrendingUp className="w-4 h-4 text-green-400" />;
      case 'down':
      case 'fall':
        return <TrendingDown className="w-4 h-4 text-red-400" />;
      default:
        return <Target className="w-4 h-4 text-blue-400" />;
    }
  };

  const getTradeStatusIcon = (status: string) => {
    switch (status) {
      case 'won':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'lost':
        return <XCircle className="w-4 h-4 text-red-400" />;
      case 'active':
        return <Clock className="w-4 h-4 text-blue-400" />;
      default:
        return <Eye className="w-4 h-4 text-gray-400" />;
    }
  };

  const renderSignals = () => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">AI Signals</h3>
        <span className="text-xs text-gray-400">{signals.length} signals</span>
      </div>
      
      {signals.length === 0 ? (
        <div className="text-center py-8">
          <Brain className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No AI signals yet</p>
          <p className="text-xs text-gray-500 mt-1">Signals will appear when AI detects opportunities</p>
        </div>
      ) : (
        <div className="space-y-2">
          {signals.map((signal) => (
            <div
              key={signal.id}
              className="bg-gray-700 rounded-lg p-3 border border-gray-600 hover:border-gray-500 transition-all cursor-pointer"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getSignalIcon(signal.direction)}
                  <span className="text-sm font-medium text-white">
                    {signal.direction.toUpperCase()}
                  </span>
                  <span className="text-xs text-gray-400">{signal.symbol}</span>
                </div>
                <div className="text-right">
                  <div className={`text-xs font-medium ${
                    signal.confidence >= 0.8 ? 'text-green-400' :
                    signal.confidence >= 0.6 ? 'text-yellow-400' : 'text-red-400'
                  }`}>
                    {(signal.confidence * 100).toFixed(0)}%
                  </div>
                  <div className="text-xs text-gray-500">{formatTime(signal.timestamp)}</div>
                </div>
              </div>
              
              <p className="text-xs text-gray-300 leading-relaxed">{signal.reason}</p>
              
              {signal.prediction !== undefined && (
                <div className="mt-2 flex items-center space-x-1">
                  <Target className="w-3 h-3 text-blue-400" />
                  <span className="text-xs text-blue-400">Prediction: {signal.prediction}</span>
                </div>
              )}
              
              <div className="mt-2 h-1 bg-gray-800 rounded">
                <div
                  className={`h-full rounded transition-all ${
                    signal.confidence >= 0.8 ? 'bg-green-400' :
                    signal.confidence >= 0.6 ? 'bg-yellow-400' : 'bg-red-400'
                  }`}
                  style={{ width: `${signal.confidence * 100}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderTrades = () => (
    <div className="space-y-3">
      {selectedTrade && (
        <ContractDetailsDialog
          trade={selectedTrade}
          open={!!selectedTrade}
          onOpenChange={(o) => {
            if (!o) setSelectedTrade(null);
          }}
        />
      )}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-white">Trade History</h3>
        <span className="text-xs text-gray-400">{trades.length} trades</span>
      </div>
      
      {trades.length === 0 ? (
        <div className="text-center py-8">
          <Activity className="w-12 h-12 text-gray-600 mx-auto mb-4" />
          <p className="text-gray-400">No trades yet</p>
          <p className="text-xs text-gray-500 mt-1">Your trading history will appear here</p>
        </div>
      ) : (
        <div className="space-y-2">
          {trades.map((trade) => (
            <div
              key={trade.id} onClick={() => trade.status === 'active' ? setSelectedTrade(trade) : null}
              className="bg-gray-700 rounded-lg p-3 border border-gray-600 hover:border-gray-500 transition-all cursor-pointer"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  {getTradeStatusIcon(trade.status)}
                  <span className="text-sm font-medium text-white">
                    {trade.direction.toUpperCase()}
                  </span>
                  <span className="text-xs text-gray-400">{trade.symbol}</span>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-400">{formatTime(trade.timestamp)}</div>
                  <div className="text-xs font-medium text-gray-300">
                    {formatCurrency(trade.stake)}
                  </div>
                </div>
              </div>
              
              <div className="flex justify-between items-center text-xs">
                <div className="text-gray-400">
                  {trade.type.replace('_', ' ').toUpperCase()} • {trade.duration}t
                </div>
                {trade.profit !== undefined && (
                  <div className={`font-medium ${
                    trade.profit >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {trade.profit >= 0 ? '+' : ''}{formatCurrency(trade.profit)}
                  </div>
                )}
              </div>
              
              {trade.prediction !== undefined && (
                <div className="mt-1 text-xs text-blue-400">
                  Prediction: {trade.prediction}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderStats = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-white">Trading Statistics</h3>
      
      <div className="grid grid-cols-2 gap-3">
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-white">{stats.totalTrades}</div>
          <div className="text-xs text-gray-400">Total Trades</div>
        </div>
        
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className={`text-2xl font-bold ${
            stats.winRate >= 70 ? 'text-green-400' :
            stats.winRate >= 50 ? 'text-yellow-400' : 'text-red-400'
          }`}>
            {stats.winRate.toFixed(1)}%
          </div>
          <div className="text-xs text-gray-400">Win Rate</div>
        </div>
        
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-green-400">{stats.wonTrades}</div>
          <div className="text-xs text-gray-400">Won</div>
        </div>
        
        <div className="bg-gray-700 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-red-400">{stats.lostTrades}</div>
          <div className="text-xs text-gray-400">Lost</div>
        </div>
      </div>
      
      <div className="space-y-3">
        <div className="bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Total Profit</span>
            <span className="text-sm font-medium text-green-400">
              {formatCurrency(stats.totalProfit)}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Total Loss</span>
            <span className="text-sm font-medium text-red-400">
              {formatCurrency(stats.totalLoss)}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-700 rounded-lg p-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-400">Net P&L</span>
            <span className={`text-sm font-medium ${
              stats.netProfit >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {stats.netProfit >= 0 ? '+' : ''}{formatCurrency(stats.netProfit)}
            </span>
          </div>
        </div>
      </div>
      
      {stats.totalTrades > 0 && (
        <div className="bg-gray-700 rounded-lg p-3">
          <div className="text-sm text-gray-400 mb-2">Performance Trend</div>
          <div className="h-2 bg-gray-800 rounded">
            <div
              className={`h-full rounded transition-all ${
                stats.winRate >= 70 ? 'bg-green-400' :
                stats.winRate >= 50 ? 'bg-yellow-400' : 'bg-red-400'
              }`}
              style={{ width: `${Math.min(stats.winRate, 100)}%` }}
            ></div>
          </div>
        </div>
      )}
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'signals':
        return renderSignals();
      case 'trades':
        return renderTrades();
      case 'stats':
        return renderStats();
      default:
        return null;
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed lg:relative top-0 right-0 h-full w-80 bg-gray-800 border-l border-gray-700 z-50 
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-full">
          {/* Tabs */}
          <div className="flex border-b border-gray-700">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-3 px-2 text-sm font-medium transition-all ${
                    activeTab === tab.id
                      ? 'text-white bg-gray-700 border-b-2 border-blue-400'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                </button>
              );
            })}
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
