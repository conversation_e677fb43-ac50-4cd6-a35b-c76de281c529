import React from 'react';
import { Wifi, WifiOff, TrendingUp, <PERSON><PERSON><PERSON>, Menu, X } from 'lucide-react';
import { useTradingStore } from '../store/tradingStore';
import { MarketSymbol } from '../types/trading';
import { useTrading } from '../hooks/useTrading';

interface HeaderProps {
  onToggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, sidebarOpen }) => {
  const { isConnected, account, currentMarket, trades } = useTradingStore();

  // Compute cumulative P&L from trades array
  const netPnL = trades.reduce((sum, t) => sum + (t.profit ?? 0), 0);
  const { changeMarket } = useTrading();

  const markets = [
    { value: 'R_100', label: '100v', name: 'Volatility 100' },
    { value: 'R_10', label: '10v', name: 'Volatility 10' },
    { value: 'R_25', label: '25v', name: 'Volatility 25' },
    { value: 'R_50', label: '50v', name: 'Volatility 50' },
    { value: 'R_75', label: '75v', name: 'Volatility 75' },
    { value: '1HZ10V', label: '10(1s)', name: 'Volatility 10 (1s)' },
    { value: '1HZ25V', label: '25(1s)', name: 'Volatility 25 (1s)' },
    { value: '1HZ50V', label: '50(1s)', name: 'Volatility 50 (1s)' },
    { value: '1HZ75V', label: '75(1s)', name: 'Volatility 75 (1s)' },
    { value: '1HZ100V', label: '100(1s)', name: 'Volatility 100 (1s)' },
  ];

  const formatBalance = (balance: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(balance);
  };

  const getConnectionStatus = () => {
    return isConnected ? (
      <div className="flex items-center space-x-1 text-green-400">
        <Wifi className="w-4 h-4" />
        <span className="text-sm font-medium">Connected</span>
      </div>
    ) : (
      <div className="flex items-center space-x-1 text-red-400">
        <WifiOff className="w-4 h-4" />
        <span className="text-sm font-medium">Disconnected</span>
      </div>
    );
  };

  return (
    <header className="bg-gray-900 border-b border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Mobile menu button */}
          <button
            onClick={onToggleSidebar}
            className="lg:hidden p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors"
          >
            {sidebarOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>

          {/* Logo */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">
                LDP <span className="text-blue-400 text-sm">Enhanced</span>
              </h1>
              <p className="text-xs text-gray-400">Binary Trading System</p>
            </div>
          </div>
        </div>

        {/* Center section - Market selector */}
        <div className="hidden md:flex items-center space-x-2">
          <span className="text-sm text-gray-400 font-medium">Market:</span>
          <div className="flex space-x-1">
            {markets.map((market) => (
              <button
                key={market.value}
                onClick={() => changeMarket(market.value as MarketSymbol)}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all ${
                  currentMarket === market.value
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white'
                }`}
                title={market.name}
              >
                {market.label}
              </button>
            ))}
          </div>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-4">
          {/* Connection status */}
          {getConnectionStatus()}

          {/* Account info */}
          {account && (
            <div className="hidden sm:flex items-center space-x-4 text-sm">
              <div className="text-right">
                <div className="text-gray-400">Balance</div>
                <div className="font-bold text-white">{formatBalance(account.balance)}</div>
              </div>
              <div className="text-right">
                <div className="text-gray-400">P&L</div>
                <div className={`font-bold ${netPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {formatBalance(netPnL)}
                </div>
              </div>
            </div>
          )}

          {/* Settings button */}
          <button className="p-2 rounded-md text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
            <Settings className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Mobile market selector */}
      <div className="md:hidden mt-3 pt-3 border-t border-gray-700">
        <div className="flex items-center space-x-2 mb-2">
          <span className="text-sm text-gray-400 font-medium">Market:</span>
        </div>
        <div className="grid grid-cols-5 gap-1">
          {markets.map((market) => (
            <button
              key={market.value}
              onClick={() => changeMarket(market.value as MarketSymbol)}
              className={`px-2 py-1 text-xs font-medium rounded transition-all ${
                currentMarket === market.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
              title={market.name}
            >
              {market.label}
            </button>
          ))}
        </div>
      </div>

      {/* Mobile account info */}
      {account && (
        <div className="sm:hidden mt-3 pt-3 border-t border-gray-700 flex justify-between text-sm">
          <div>
            <span className="text-gray-400">Balance: </span>
            <span className="font-bold text-white">{formatBalance(account.balance)}</span>
          </div>
          <div>
            <span className="text-gray-400">P&L: </span>
            <span className={`font-bold ${netPnL >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatBalance(netPnL)}
            </span>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
