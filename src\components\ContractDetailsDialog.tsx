import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from './ui/dialog';
import { Trade } from '../types/trading';
import { getDerivAPI } from '../services/derivAPI';
import { Loader2 } from 'lucide-react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  Scatter,
  LabelList,
  XAxis,
  YAxis,
  Tooltip,
  ReferenceLine,
  CartesianGrid,
} from 'recharts';
import { useMarketData } from '../store/tradingStore';

interface ContractDetailsDialogProps {
  trade: Trade | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface OpenContract {
  contract_id: number;
  status: string;
  is_valid_to_sell: number;
  entry_spot?: string;
  current_spot?: string;
  current_spot_time?: number;
  barrier?: string;
  high_barrier?: string;
  low_barrier?: string;
  bid_price?: number;
  buy_price?: number;
  payout?: number;
  profit?: number;
  profit_percentage?: number;
  date_start?: number;
  date_expiry?: number;
  tick_count?: number;
  [key: string]: any;
}

/**
 * Simple modal that fetches and displays live open-contract details for a trade.
 * It polls once on open and relies on ongoing proposal_open_contract subscription
 * inside DerivAPIService to keep the store updated; this component only shows
 * a snapshot for brevity.
 */
const ContractDetailsDialog: React.FC<ContractDetailsDialogProps> = ({ trade, open, onOpenChange }) => {
  type Point = { ts: number; price: number; idx: number };
  const [chartData, setChartData] = useState<Point[]>([]);
  const lastPoint = chartData.length ? chartData[chartData.length - 1] : null;
  const market = useMarketData(trade?.symbol);
  const latestPrice = market?.lastPrice;

  const [details, setDetails] = useState<OpenContract | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [entryMs, setEntryMs] = useState<number | null>(null);
  const [expiryMs, setExpiryMs] = useState<number | null>(null);

  // Fetch static contract details
  useEffect(() => {
    if (open && trade) {
      setLoading(true);
      setError(null);
      getDerivAPI()
        .getOpenContract(String(trade.id))
        .then((data: any) => {
          setDetails(data);
           setEntryMs(data?.date_start ? data.date_start * 1000 : null);
           setExpiryMs(data?.date_expiry ? data.date_expiry * 1000 : null);
        })
        .catch((err: any) => setError(err.message || 'Failed to fetch contract'))
        .finally(() => setLoading(false));
    } else {
      setDetails(null);
    }
  }, [open, trade]);

  // Subscribe to live ticks for chart
  useEffect(() => {
    let subId: string | undefined;
    if (open && trade) {
      setChartData([]);
      getDerivAPI()
        .subscribeToTicks(trade.symbol)
        .then((id) => {
          subId = id;
        })
        .catch(console.error);
    }
    return () => {
      if (subId) {
        getDerivAPI()
          .unsubscribe(subId)
          .catch(console.error);
      }
    };
  }, [open, trade]);

  // Append latest price to chart data
  useEffect(() => {
    if (!open || !trade || latestPrice === undefined) return;
    const nowTs = Date.now();
    if (entryMs && nowTs < entryMs) return;
    if (expiryMs && nowTs > expiryMs) return;

    setChartData((prev) => {
      const nextIdx = prev.length ? prev[prev.length - 1].idx + 1 : 1;
      const newArr = [...prev, { ts: nowTs, price: latestPrice as number, idx: nextIdx }]
        .filter(pt => !entryMs || pt.ts >= entryMs);
      return newArr.slice(-300);
    });
  }, [latestPrice, open, trade, entryMs, expiryMs]);

  const fmt = (value: any, digits = 2) => {
    if (value === undefined || value === null) return '-';
    if (typeof value === 'number') return value.toFixed(digits);
    return String(value);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl bg-gray-800 border-gray-700 text-white">
        <DialogHeader>
          <DialogTitle>Contract Details</DialogTitle>
          {trade && (
            <DialogDescription className="text-gray-400">
              {trade.symbol} • {trade.direction.toUpperCase()} • #{trade.id}
            </DialogDescription>
          )}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-10">
            <Loader2 className="w-6 h-6 animate-spin text-blue-400" />
          </div>
        ) : error ? (
          <p className="text-red-400 text-sm">{error}</p>
        ) : details ? (
          <div className="grid grid-cols-2 gap-4 text-sm mt-4">
            <div>
              <span className="text-gray-400">Status:</span> {details.status}
            </div>
            <div>
              <span className="text-gray-400">Stake:</span> ${fmt(trade?.stake)}
            </div>
            <div>
              <span className="text-gray-400">Potential Payout:</span> ${fmt(details.payout)}
            </div>
            <div>
              <span className="text-gray-400">Profit/Loss:</span>{' '}
              <span className={details.profit && details.profit >= 0 ? 'text-green-400' : 'text-red-400'}>
                {details.profit ? (details.profit >= 0 ? '+' : '') + fmt(details.profit) : '-'}
              </span>
            </div>
            <div>
              <span className="text-gray-400">Entry Spot:</span> {fmt(details.entry_spot)}
            </div>
            <div>
              <span className="text-gray-400">Current Spot:</span> {fmt(details.current_spot)}
            </div>
            {details.barrier && (
              <div>
                <span className="text-gray-400">Barrier:</span> {details.barrier}
              </div>
            )}
            {details.high_barrier && (
              <div>
                <span className="text-gray-400">High Barrier:</span> {details.high_barrier}
              </div>
            )}
            {details.low_barrier && (
              <div>
                <span className="text-gray-400">Low Barrier:</span> {details.low_barrier}
              </div>
            )}
            <div>
              <span className="text-gray-400">Start Time:</span>{' '}
              {details.date_start ? new Date(details.date_start * 1000).toLocaleTimeString() : '-'}
            </div>
            <div>
              <span className="text-gray-400">Expiry Time:</span>{' '}
              {details.date_expiry ? new Date(details.date_expiry * 1000).toLocaleTimeString() : '-'}
            </div>
            {details.tick_count !== undefined && (
              <div>
                <span className="text-gray-400">Tick Count:</span> {details.tick_count}
              </div>
            )}
          </div>
        ) : (
          <p className="text-sm text-gray-400">No details available.</p>
        )}

        <DialogFooter className="mt-6">
          <DialogClose className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm">
            Close
          </DialogClose>
        </DialogFooter>

        {/* Live chart */}
        {trade && (
          <div className="h-64 mt-8">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                 <defs>
                   <linearGradient id="priceGradient" x1="0" y1="0" x2="0" y2="1">
                     <stop offset="0%" stopColor="#4ade80" stopOpacity={0.25} />
                     <stop offset="100%" stopColor="#4ade80" stopOpacity={0} />
                   </linearGradient>
                 </defs>
                <CartesianGrid stroke="#2d3748" strokeDasharray="3 3" />
                <XAxis dataKey="ts" domain={['auto', 'auto']} type="number" tick={false} axisLine={false} />
                <YAxis
                  domain={['dataMin', 'dataMax']}
                  tick={{ fill: '#a0aec0', fontSize: 12 }}
                  axisLine={false}
                  width={60}
                />
                <Tooltip
                  labelFormatter={(label) => new Date(label).toLocaleTimeString()}
                  formatter={(value: any) => value.toFixed(2)}
                  contentStyle={{ backgroundColor: '#1a202c', borderColor: '#4a5568' }}
                  labelStyle={{ color: '#a0aec0' }}
                />
                <Area type="monotone" dataKey="price" stroke={undefined} fill="url(#priceGradient)" isAnimationActive={false} />
                 <Line type="monotone" dataKey="price" stroke="#4ade80" dot={false} isAnimationActive={false} />
                 {/* Tick markers */}
                 {chartData.length > 0 && (
                   <Scatter data={chartData} fill="#d1d5db" shape="circle">
                     <LabelList dataKey="idx" position="top" fill="#d1d5db" fontSize={10} />
                   </Scatter>
                 )}
                 {/* Entry dot */}
                 {chartData[0] && (
                   <Scatter data={[chartData[0]]} fill="#ff5555" shape="circle" />
                 )}
                {entryMs && (
                   <ReferenceLine x={entryMs} stroke="#9ca3af" strokeWidth={2} />
                 )}
                 {trade.entryPrice !== undefined && (
                   <ReferenceLine y={trade.entryPrice} stroke="#22d3ee" strokeDasharray="3 3" label={{ value: 'Entry', fill: '#22d3ee', position: 'left' }} />
                 )}
                {details?.barrier && (
                  <ReferenceLine y={parseFloat(details.barrier)} stroke="#f472b6" strokeDasharray="3 3" label={{ value: 'Barrier', fill: '#f472b6', position: 'left' }} />
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ContractDetailsDialog;
