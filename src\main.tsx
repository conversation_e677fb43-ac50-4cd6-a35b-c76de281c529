import React from 'react';
import { createRoot } from 'react-dom/client'
import { ErrorBoundary } from './components/ErrorBoundary.tsx'
import './index.css'
import App from './App.tsx'
import { initializeDerivAPI } from './services/derivAPI';

// Initialize the Deriv API service singleton before the app starts
const API_TOKEN = 'lX8QAY83KmTSa4P'; // Use user's demo account token
initializeDerivAPI(API_TOKEN);

createRoot(document.getElementById('root')!).render(
  <ErrorBoundary>
    <App />
  </ErrorBoundary>
)
