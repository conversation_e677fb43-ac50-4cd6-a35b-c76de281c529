import React, { useState, useMemo } from 'react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine,
  Rectangle,
  ComposedChart
} from 'recharts';
import { BarChart3, TrendingUp, Activity, Zap } from 'lucide-react';
import { ChartData, MarketData } from '../types/trading';
import { useTrading } from '../hooks/useTrading';
import { useTradingStore } from '../store/tradingStore';

interface ChartProps {
  className?: string;
}

const Chart: React.FC<ChartProps> = ({ className = '' }) => {
  const { currentMarket } = useTradingStore();
  const { getCurrentChartData, getCurrentMarketData } = useTrading();
  const [chartType, setChartType] = useState<'line' | 'candle'>('line');
  const [timeframe, setTimeframe] = useState<'1m' | '5m' | '15m' | '1h'>('5m');

  const chartData = getCurrentChartData();
  const marketData = getCurrentMarketData();

  const processedData = useMemo(() => {
    if (!chartData || chartData.length === 0) {
      return [];
    }
    return chartData.map((item, index) => ({
      ...item,
      time: new Date(item.timestamp).toLocaleTimeString(),
      price: item.close,
      volume: item.volume || Math.random() * 1000, // Mock volume if not available
    }));
  }, [chartData]);

  const latestPrice = chartData.length > 0 ? chartData[chartData.length - 1].close : 0;
  const previousPrice = chartData.length > 1 ? chartData[chartData.length - 2].close : latestPrice;
  const priceChange = latestPrice - previousPrice;
  const priceChangePercent = previousPrice !== 0 ? (priceChange / previousPrice) * 100 : 0;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg">
          <p className="text-gray-300 text-sm">{`Time: ${label}`}</p>
          <p className="text-blue-400 font-medium">{`Price: ${data.close?.toFixed(4)}`}</p>
          {chartType === 'candle' && (
            <>
              <p className="text-green-400 text-sm">{`Open: ${data.open?.toFixed(4)}`}</p>
              <p className="text-red-400 text-sm">{`High: ${data.high?.toFixed(4)}`}</p>
              <p className="text-yellow-400 text-sm">{`Low: ${data.low?.toFixed(4)}`}</p>
            </>
          )}
        </div>
      );
    }
    return null;
  };

  const renderCandle = (props: any) => {
    const { x, y, width, height, open, close, high, low } = props;
    const isUp = close >= open;
    const color = isUp ? '#10B981' : '#EF4444';
    const strokeColor = isUp ? '#059669' : '#DC2626';
    
    // Calculate candle dimensions
    const candleX = x - width / 2;
    const candleY = isUp ? y : y + (open - close);
    const candleHeight = Math.max(Math.abs(close - open), 1); // Ensure minimum height for visibility
    
    return (
      <g>
        {/* Candle wick */}
        <line
          x1={x}
          y1={y + (high < low ? (high - open) : (low - open))}
          x2={x}
          y2={y + (high < low ? (low - open) : (high - open))}
          stroke={strokeColor}
          strokeWidth={1}
        />
        {/* Candle body */}
        <rect
          x={candleX}
          y={candleY}
          width={width}
          height={candleHeight}
          fill={color}
          stroke={strokeColor}
          strokeWidth={1}
        />
      </g>
    );
  };

  const renderChart = () => {
    if (processedData.length === 0) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <Activity className="w-12 h-12 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400">Loading chart data...</p>
          </div>
        </div>
      );
    }

    const chartProps = {
      data: processedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
      children: (
        <>
          <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
          <XAxis 
            dataKey="time" 
            stroke="#9CA3AF"
            fontSize={12}
          />
          <YAxis 
            stroke="#9CA3AF"
            fontSize={12}
            domain={['dataMin - 0.001', 'dataMax + 0.001']}
          />
          <Tooltip content={<CustomTooltip />} />
          {chartType === 'candle' ? (
            <Line
              type="monotone"
              dataKey="close"
              stroke="transparent"
              dot={false}
              activeDot={false}
            >
              {processedData.map((entry, index) => (
                <g key={`candle-${index}`}>
                  {renderCandle({
                    x: index * (100 / processedData.length) + (50 / processedData.length),
                    y: entry.open,
                    width: Math.max(5, 100 / processedData.length * 0.6),
                    height: Math.abs(entry.close - entry.open),
                    open: entry.open,
                    close: entry.close,
                    high: entry.high,
                    low: entry.low
                  })}
                </g>
              ))}
            </Line>
          ) : (
            <Line
              type="monotone"
              dataKey="close"
              stroke="#3B82F6"
              strokeWidth={2}
              dot={false}
              activeDot={{ r: 4, stroke: '#3B82F6', strokeWidth: 2, fill: '#1F2937' }}
            />
          )}
          {/* Support and resistance lines */}
          {chartData.length > 0 && (
            <>
              <ReferenceLine 
                y={Math.max(...chartData.map(d => d.high))} 
                stroke="#EF4444" 
                strokeDasharray="5 5" 
                label={{ value: "Resistance", position: "top" }}
              />
              <ReferenceLine 
                y={Math.min(...chartData.map(d => d.low))} 
                stroke="#10B981" 
                strokeDasharray="5 5" 
                label={{ value: "Support", position: "bottom" }}
              />
            </>
          )}
        </>
      )
    };

    return (
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart {...chartProps} />
      </ResponsiveContainer>
    );
  };

  return (
    <div className={`bg-gray-800 rounded-lg border border-gray-700 ${className}`}>
      {/* Chart header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5 text-blue-400" />
            <h3 className="text-lg font-semibold text-white">{currentMarket} Chart</h3>
          </div>
          
          {/* Price info */}
          {marketData && (
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <div className="text-xl font-bold text-white">
                  {latestPrice.toFixed(4)}
                </div>
                <div className={`flex items-center space-x-1 text-sm ${
                  priceChange >= 0 ? 'text-green-400' : 'text-red-400'
                }`}>
                  <TrendingUp className={`w-3 h-3 ${priceChange < 0 ? 'rotate-180' : ''}`} />
                  <span>{priceChange >= 0 ? '+' : ''}{priceChange.toFixed(4)}</span>
                  <span>({priceChangePercent >= 0 ? '+' : ''}{priceChangePercent.toFixed(2)}%)</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Chart controls */}
        <div className="flex items-center space-x-2">
          {/* Chart type selector */}
          <div className="flex bg-gray-700 rounded-lg p-1">
            <button
              onClick={() => setChartType('line')}
              className={`px-3 py-1 text-sm rounded transition-all ${
                chartType === 'line'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Line
            </button>
            <button
              onClick={() => setChartType('candle')}
              className={`px-3 py-1 text-sm rounded transition-all ${
                chartType === 'candle'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Candle
            </button>
          </div>

          {/* Timeframe selector */}
          <div className="flex bg-gray-700 rounded-lg p-1">
            {(['1m', '5m', '15m', '1h'] as const).map((tf) => (
              <button
                key={tf}
                onClick={() => setTimeframe(tf)}
                className={`px-2 py-1 text-xs rounded transition-all ${
                  timeframe === tf
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                {tf}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Chart content */}
      <div className="h-96 p-4">
        {renderChart()}
      </div>

      {/* Chart footer with latest digits */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-300">Latest Digits</h4>
          <div className="flex space-x-1">
            {marketData && marketData.digits && marketData.digits.length > 0 ? (
              marketData.digits.map((digit, index) => (
                <span
                  key={index}
                  className={`w-8 h-8 flex items-center justify-center rounded text-sm font-bold ${
                    digit % 2 === 0 ? 'bg-red-600 text-white' : 'bg-blue-600 text-white'
                  }`}
                >
                  {digit}
                </span>
              ))
            ) : (
              <div className="text-gray-400 text-sm italic">Waiting for tick data...</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Chart;
